#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化版本训练脚本
专为32G内存系统优化，避免内存溢出

使用方法:
    python start_training_memory_optimized.py

作者: AI Assistant
日期: 2024-12-19
"""

import os
import yaml
import time
import json
import psutil
from pathlib import Path

def check_system_resources():
    """检查系统资源"""
    print("🔍 检查系统资源...")
    
    # 检查内存
    memory = psutil.virtual_memory()
    print(f"💾 系统内存: {memory.total / (1024**3):.1f}GB")
    print(f"💾 可用内存: {memory.available / (1024**3):.1f}GB")
    print(f"💾 内存使用率: {memory.percent:.1f}%")
    
    # 检查CPU
    cpu_count = psutil.cpu_count()
    print(f"🖥️  CPU核心数: {cpu_count}")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"🚀 GPU: {gpu_name}")
            print(f"🚀 GPU内存: {gpu_memory:.1f}GB")
            return True, memory.available / (1024**3), cpu_count
        else:
            print("💻 未检测到GPU，将使用CPU训练")
            return False, memory.available / (1024**3), cpu_count
    except ImportError:
        print("❌ PyTorch未安装")
        return False, memory.available / (1024**3), cpu_count

def get_optimal_config(has_gpu, available_memory_gb, cpu_count):
    """根据系统资源获取最优配置"""
    print(f"\n⚙️  根据系统资源生成最优配置...")
    
    if has_gpu:
        # GPU配置
        if available_memory_gb > 20:
            # 充足内存配置
            config = {
                'data': 'configs/dataset.yaml',
                'epochs': 100,
                'imgsz': 640,
                'batch': 4,
                'device': 'cuda',
                'workers': min(4, cpu_count // 2),
                'name': 'human_detection_gpu_optimized',
                'amp': True,
                'cache': False,  # 不使用RAM缓存
                'optimizer': 'AdamW',
                'patience': 50,
                'save_period': 10,
                'verbose': True,
            }
            print("🚀 使用GPU高性能配置")
        else:
            # 内存受限配置
            config = {
                'data': 'configs/dataset.yaml',
                'epochs': 80,
                'imgsz': 512,  # 减小图像尺寸
                'batch': 2,    # 减小批次
                'device': 'cuda',
                'workers': min(2, cpu_count // 4),
                'name': 'human_detection_gpu_memory_limited',
                'amp': True,
                'cache': False,
                'optimizer': 'AdamW',
                'patience': 40,
                'save_period': 10,
                'verbose': True,
            }
            print("🚀 使用GPU内存受限配置")
    else:
        # CPU配置
        if available_memory_gb > 20:
            config = {
                'data': 'configs/dataset.yaml',
                'epochs': 50,
                'imgsz': 416,
                'batch': 2,
                'device': 'cpu',
                'workers': min(2, cpu_count // 4),
                'name': 'human_detection_cpu_optimized',
                'amp': False,
                'cache': False,
                'optimizer': 'SGD',
                'patience': 25,
                'save_period': 5,
                'verbose': True,
            }
            print("💻 使用CPU标准配置")
        else:
            config = {
                'data': 'configs/dataset.yaml',
                'epochs': 30,
                'imgsz': 320,  # 进一步减小图像尺寸
                'batch': 1,    # 最小批次
                'device': 'cpu',
                'workers': 1,
                'name': 'human_detection_cpu_memory_limited',
                'amp': False,
                'cache': False,
                'optimizer': 'SGD',
                'patience': 15,
                'save_period': 5,
                'verbose': True,
            }
            print("💻 使用CPU内存受限配置")
    
    return config

def monitor_memory_usage():
    """监控内存使用"""
    memory = psutil.virtual_memory()
    return memory.percent

def start_optimized_training():
    """开始优化训练"""
    print("\n🚀 开始内存优化训练...")
    
    try:
        from ultralytics import YOLO
        
        # 检查系统资源
        has_gpu, available_memory, cpu_count = check_system_resources()
        
        # 获取最优配置
        config = get_optimal_config(has_gpu, available_memory, cpu_count)
        
        print("\n📋 训练配置:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        # 初始化模型
        print("\n🔧 初始化YOLO模型...")
        try:
            model = YOLO('yolo11n.pt')  # 使用更小的nano版本
            print("✅ 使用YOLOv11n模型（内存优化）")
        except Exception as e:
            print(f"⚠️  YOLOv11n不可用，尝试使用YOLOv8n: {e}")
            model = YOLO('yolov8n.pt')
            print("✅ 使用YOLOv8n模型（内存优化）")
        
        # 开始训练前的内存检查
        initial_memory = monitor_memory_usage()
        print(f"\n📊 训练前内存使用率: {initial_memory:.1f}%")
        
        if initial_memory > 80:
            print("⚠️  警告：系统内存使用率已经很高，建议关闭其他程序")
            input("按Enter键继续，或Ctrl+C取消...")
        
        print("\n⏰ 开始训练... (这可能需要一些时间)")
        print("💡 提示: 您可以按 Ctrl+C 来停止训练")
        print("💡 提示: 训练过程中会定期检查内存使用情况")
        
        start_time = time.time()
        
        # 开始训练
        results = model.train(**config)
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n🎉 训练完成!")
        print(f"⏱️  总训练时间: {training_time/3600:.2f} 小时")
        print(f"📁 结果保存在: {results.save_dir}")
        print(f"🏆 最佳模型: {results.save_dir}/weights/best.pt")
        
        # 保存训练信息
        training_info = {
            'training_time': training_time,
            'config': config,
            'save_dir': str(results.save_dir),
            'best_model': str(results.save_dir / 'weights' / 'best.pt'),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'system_info': {
                'has_gpu': has_gpu,
                'available_memory_gb': available_memory,
                'cpu_count': cpu_count
            }
        }
        
        with open('training_info_optimized.json', 'w', encoding='utf-8') as f:
            json.dump(training_info, f, indent=2, ensure_ascii=False)
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        print("💡 建议：")
        print("   1. 检查是否有足够的磁盘空间")
        print("   2. 关闭其他占用内存的程序")
        print("   3. 尝试使用更小的批次大小")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 浓烟环境人体目标检测 - 内存优化训练")
    print("=" * 60)
    
    # 检查环境
    try:
        import torch
        import ultralytics
        print("✅ 所有必要的包都已安装")
    except ImportError as e:
        print(f"❌ 缺少必要的包: {e}")
        print("请运行: pip install ultralytics torch torchvision")
        return
    
    # 检查数据集
    if not Path("wuxi_video_2").exists():
        print("❌ 数据集目录不存在")
        return
    
    if not Path("configs/dataset.yaml").exists():
        print("❌ 配置文件不存在，请先运行 python start_training.py 生成配置文件")
        return
    
    # 开始训练
    success = start_optimized_training()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 内存优化训练流程完成!")
        print("📖 查看详细使用说明: docs/模型训练指南.md")
        print("🚀 下一步: 运行 python src/integrated_system.py 测试完整系统")
        print("=" * 60)
    else:
        print("\n❌ 训练未成功完成")

if __name__ == "__main__":
    main()
