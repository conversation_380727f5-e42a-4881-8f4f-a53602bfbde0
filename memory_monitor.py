#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存监控脚本
实时监控系统内存使用情况

使用方法:
    python memory_monitor.py

作者: AI Assistant
日期: 2024-12-19
"""

import psutil
import time
import os
import signal
import sys
from datetime import datetime

class MemoryMonitor:
    def __init__(self, interval=5, threshold=85):
        """
        初始化内存监控器
        
        Args:
            interval: 监控间隔（秒）
            threshold: 内存使用率警告阈值（百分比）
        """
        self.interval = interval
        self.threshold = threshold
        self.running = True
        self.log_file = f"memory_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n🛑 收到停止信号，正在保存日志...")
        self.running = False
    
    def get_memory_info(self):
        """获取内存信息"""
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        return {
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3),
            'percent': memory.percent,
            'swap_total_gb': swap.total / (1024**3),
            'swap_used_gb': swap.used / (1024**3),
            'swap_percent': swap.percent
        }
    
    def get_process_info(self):
        """获取占用内存最多的进程信息"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_percent', 'memory_info']):
            try:
                proc_info = proc.info
                if proc_info['memory_percent'] > 1.0:  # 只显示占用超过1%内存的进程
                    processes.append({
                        'pid': proc_info['pid'],
                        'name': proc_info['name'],
                        'memory_percent': proc_info['memory_percent'],
                        'memory_mb': proc_info['memory_info'].rss / (1024**2)
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        # 按内存使用率排序
        processes.sort(key=lambda x: x['memory_percent'], reverse=True)
        return processes[:10]  # 返回前10个
    
    def log_to_file(self, message):
        """记录到日志文件"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")
    
    def display_memory_info(self, memory_info, processes):
        """显示内存信息"""
        # 清屏
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("=" * 60)
        print("🔍 实时内存监控")
        print("=" * 60)
        print(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 监控间隔: {self.interval}秒")
        print(f"⚠️  警告阈值: {self.threshold}%")
        print()
        
        # 内存信息
        print("💾 内存使用情况:")
        print(f"   总内存: {memory_info['total_gb']:.1f}GB")
        print(f"   已使用: {memory_info['used_gb']:.1f}GB")
        print(f"   可用内存: {memory_info['available_gb']:.1f}GB")
        print(f"   使用率: {memory_info['percent']:.1f}%")
        
        # 内存使用率状态
        if memory_info['percent'] >= 90:
            status = "🔴 危险"
        elif memory_info['percent'] >= self.threshold:
            status = "🟡 警告"
        else:
            status = "🟢 正常"
        print(f"   状态: {status}")
        
        # 交换内存
        if memory_info['swap_total_gb'] > 0:
            print(f"\n💿 交换内存:")
            print(f"   总交换: {memory_info['swap_total_gb']:.1f}GB")
            print(f"   已使用: {memory_info['swap_used_gb']:.1f}GB")
            print(f"   使用率: {memory_info['swap_percent']:.1f}%")
        
        # 进程信息
        print(f"\n🔝 内存占用最高的进程:")
        print(f"{'PID':<8} {'进程名':<20} {'内存%':<8} {'内存(MB)':<10}")
        print("-" * 50)
        for proc in processes[:5]:
            print(f"{proc['pid']:<8} {proc['name'][:19]:<20} {proc['memory_percent']:<7.1f}% {proc['memory_mb']:<9.1f}")
        
        # 建议
        print(f"\n💡 建议:")
        if memory_info['percent'] >= 90:
            print("   🔴 内存使用率过高！建议立即关闭不必要的程序")
        elif memory_info['percent'] >= self.threshold:
            print("   🟡 内存使用率较高，建议关闭一些程序")
        else:
            print("   🟢 内存使用正常")
        
        print(f"\n📝 日志文件: {self.log_file}")
        print("按 Ctrl+C 停止监控")
    
    def run(self):
        """运行监控"""
        print("🚀 开始内存监控...")
        print(f"📝 日志将保存到: {self.log_file}")
        
        # 记录开始监控
        self.log_to_file("开始内存监控")
        
        try:
            while self.running:
                # 获取内存信息
                memory_info = self.get_memory_info()
                processes = self.get_process_info()
                
                # 显示信息
                self.display_memory_info(memory_info, processes)
                
                # 记录到日志
                log_message = f"内存使用率: {memory_info['percent']:.1f}%, 可用: {memory_info['available_gb']:.1f}GB"
                self.log_to_file(log_message)
                
                # 检查警告
                if memory_info['percent'] >= 90:
                    warning_msg = f"🔴 危险：内存使用率达到 {memory_info['percent']:.1f}%"
                    self.log_to_file(warning_msg)
                elif memory_info['percent'] >= self.threshold:
                    warning_msg = f"🟡 警告：内存使用率达到 {memory_info['percent']:.1f}%"
                    self.log_to_file(warning_msg)
                
                # 等待
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.log_to_file("停止内存监控")
            print(f"\n✅ 监控已停止，日志已保存到: {self.log_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='内存监控工具')
    parser.add_argument('--interval', '-i', type=int, default=5, help='监控间隔（秒），默认5秒')
    parser.add_argument('--threshold', '-t', type=int, default=85, help='警告阈值（百分比），默认85%')
    
    args = parser.parse_args()
    
    # 创建监控器
    monitor = MemoryMonitor(interval=args.interval, threshold=args.threshold)
    
    # 运行监控
    monitor.run()

if __name__ == "__main__":
    main()
